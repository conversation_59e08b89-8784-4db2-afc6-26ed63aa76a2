# Product & Engineering Document: [SEC] Prevent Injection and XSS Vulnerabilities

**Document Owner:** <PERSON><PERSON>, Head of Engineering  
**Status:** Approved for Implementation  
**Last Updated:** July 7, 2025

---

### **1. Product Requirements & Vision (PRD)**

#### **1.1. Introduction & Problem Statement**

As we approach our production launch, ensuring the security and integrity of the CashbackDeals platform is paramount. An internal audit has identified potential vectors for injection and Cross-Site Scripting (XSS) attacks. These vulnerabilities, if exploited, could lead to compromised user data, session hijacking, and damage to the platform's reputation.

The current security model relies on basic string-level sanitization, which is insufficient. It also contains a critical flaw where unescaped database content is rendered directly into the browser, creating a significant Stored XSS risk. This project will address these issues by implementing a robust, multi-layered security strategy.

#### **1.2. Product Goals & Business Impact**

* **Goal:** To eliminate all known injection and XSS vulnerabilities from the application, ensuring the platform is secure for public launch.
* **Business Impact:**
    * **Protect Users:** Safeguard user accounts and data from malicious actors.
    * **Build Trust:** Demonstrate a commitment to security, building confidence with users, partners, and future investors.
    * **Ensure Stability:** Prevent security-related downtime or data corruption.
    * **Achieve Production Readiness:** Fulfill a critical prerequisite for a safe and successful production launch.

#### **1.3. User Stories & Scope**

1.  **[SEC-1] Implement Strict Input Validation:** As a developer, I want to implement strict, schema-based validation for all user-controllable input so that I can prevent injection attacks and ensure data integrity.
2.  **[SEC-2] Eradicate Cross-Site Scripting (XSS):** As a developer, I want to ensure all user-generated content is properly escaped or sanitized before being rendered in the browser so that I can completely prevent XSS attacks.

---

### **2. Technical Architecture & Implementation Plan**

This section details the technical strategy for achieving the goals outlined above. We will adopt a two-pronged approach: **1) Proactive Input Validation** at the edge and **2) Defensive Output Encoding** at the view layer.

#### **2.1. Initiative 1: Schema-Based Input Validation with Zod**

We are adopting **Zod**, a TypeScript-first schema validation library, as the cornerstone of our input security strategy. Zod allows us to define a single source of truth for our data structures that enforces correctness and provides static TypeScript types automatically. This is a significant upgrade from our previous manual validation methods.

**Why Zod?**
* **TypeScript-First:** Seamless integration with our Next.js and TypeScript stack.
* **End-to-End Type Safety:** A single schema can be shared and enforced on both the client and server.
* **Declarative & Readable:** Improves code quality and makes validation logic easier to maintain.
* **Performance:** We will use **Zod version 4**, which is significantly faster and has a much smaller bundle size than its predecessor, making it ideal for our production environment.

**Implementation Details:**

1.  **Installation & Setup:**
    ```bash
    npm install zod
    ```
    We will use the recommended `v4` import path to leverage the latest features and performance enhancements.
    ```typescript
    import { z } from "zod/v4";
    ```

2.  **Schema Definition:** For every API endpoint and Server Action, a Zod schema will be created. This schema will define the precise shape, types, and constraints of the expected data.

    *Example: Search API Schema*
    ```typescript
    const searchApiSchema = z.object({
      q: z.string().min(3, "Query must be at least 3 characters.").max(50, "Query cannot exceed 50 characters."),
      // Add other filter params like category, brand, etc.
    });
    ```

3.  **Validation in Route Handlers:** In each API route, we will use the `.safeParse()` method to validate incoming data against the schema. If validation fails, the request will be immediately rejected with a `400 Bad Request` response.

    *Example: Securing the Search API (`/src/app/api/search/route.ts`)*
    ```typescript
    import { NextRequest, NextResponse } from 'next/server';
    import { z } from "zod/v4";

    const searchApiSchema = z.object({
      q: z.string().min(3, "Query must be at least 3 characters.").max(50, "Query cannot exceed 50 characters."),
    });

    export async function GET(request: NextRequest) {
      const params = Object.fromEntries(request.nextUrl.searchParams);
      const validation = searchApiSchema.safeParse(params);

      if (!validation.success) {
        return NextResponse.json({ error: "Invalid request parameters", details: validation.error.flatten() }, { status: 400 });
      }

      // Proceed with validated and type-safe data
      const { q } = validation.data;
      // ... database logic using the validated query ...
      return NextResponse.json({ success: true, data: [] });
    }
    ```

---

#### **2.2. Initiative 2: Eradicating XSS with Safe Output Rendering**

This initiative remediates our most critical vulnerability: Stored XSS via `dangerouslySetInnerHTML`. The new policy is to **never trust data**. All content rendered into the view must be treated as unsafe by default and be explicitly sanitized.

**Implementation Details:**

1.  **Code Audit:** The entire frontend codebase will be audited for all instances of `dangerouslySetInnerHTML`.

2.  **Sanitization with `isomorphic-dompurify`:** For the few cases where rich text (e.g., `<b>` or `<i>` tags in a description) is a hard product requirement, we will use `isomorphic-dompurify`. This library will strip all potentially dangerous HTML and attributes, leaving only a pre-approved safe list.

    **Installation:**
    ```bash
    npm install isomorphic-dompurify
    ```

    *Example: Safely Rendering a Product Description (`/src/app/products/[id]/components/ProductInfo.tsx`)*
    ```tsx
    import DOMPurify from 'isomorphic-dompurify';

    interface ProductInfoProps {
      product: {
        description: string;
      };
    }

    export function ProductInfo({ product }: ProductInfoProps) {
      // Define a strict allow-list for HTML tags and attributes
      const sanitizedDescription = DOMPurify.sanitize(product.description, {
        ALLOWED_TAGS: ['b', 'i', 'p', 'br'],
        ALLOWED_ATTR: [] // No attributes allowed
      });

      return (
        <div className="prose">
          {/* This is now safe to render */}
          <div dangerouslySetInnerHTML={{ __html: sanitizedDescription }} />
        </div>
      );
    }
    ```

3.  **Default to React's Escaping:** For all other cases, `dangerouslySetInnerHTML` will be removed entirely. We will rely on React's native JSX encoding, which automatically escapes content and prevents HTML from being rendered, thus neutralizing any XSS threat.

    *Example: Rendering a simple product name*
    ```tsx
    // Unsafe way (will be removed)
    // <h1 dangerouslySetInnerHTML={{ __html: product.name }} />

    // Safe, default React way
    <h1>{product.name}</h1>
    ```

---

### **3. Success Metrics & Testing**

* **Primary Metric:** 100% of all user-controllable input vectors (API endpoints, server actions, forms) are protected by Zod schema validation.
* **Secondary Metric:** Zero instances of `dangerouslySetInnerHTML` exist in the codebase without being fed through `DOMPurify`.
* **Testing:**
    * **Unit Tests:** Will be written for all Zod schemas to ensure they correctly validate and reject data.
    * **Integration Tests:** API tests will be updated to include malicious payloads (e.g., script tags, oversized strings) to confirm that `400` error responses are returned as expected.
    * **Manual QA:** A dedicated QA cycle will focus on attempting to inject malicious scripts through all user input fields in the application.

---

### **4. Summary**

This security sprint will significantly enhance the resilience of the CashbackDeals platform against injection and XSS attacks by:

- Enforcing strict, schema-based validation with Zod across all user inputs.
- Eliminating unsafe HTML rendering practices and adopting safe sanitization with isomorphic-dompurify.
- Implementing a strict Content Security Policy (CSP) as a defense-in-depth measure.
- Expanding testing coverage to include injection and XSS attack vectors.
- Continuing to monitor and audit security headers and application behavior.

These measures will ensure the platform is secure, stable, and ready for production launch, protecting both users and business interests.



