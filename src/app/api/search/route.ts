/**
 * Refactored Search API Route - Phase 2A Step 4
 * 
 * This route has been refactored to use the shared server-side data layer
 * for improved security, performance, and maintainability.
 * 
 * Key improvements:
 * - Uses shared data layer functions instead of direct Supabase queries
 * - Eliminates public key usage in favor of secure server-side access
 * - Consistent error handling and response formats
 * - Better caching strategy
 * - Reduced code complexity from 249 lines to ~150 lines
 * - Maintains backward compatibility with existing response format
 */

import { NextRequest, NextResponse } from 'next/server'
import { searchProducts } from '@/lib/data'
import type { SearchFilters, ApiResponse } from '@/lib/data/types'
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
import { validatePaginationParams, validateSearchQuery, validateFilterParams } from '@/lib/utils'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';

/**
 * Legacy response format for backward compatibility
 */
interface LegacySearchProduct {
  id: string
  name: string
  description: string
  images: string[]
  status: string
  brand: {
    id: string
    name: string
    logo_url: string
  } | null
  category: {
    id: string
    name: string
  } | null
  minPrice: number | null
  cashbackAmount: number
  retailerOffers: Array<{
    retailer: {
      name: string
      logo_url: string
    }
    price: number
    cashback: number
    url: string
    stock_status: string
    created_at: string
    valid_until?: string
  }>
  promotion: {
    id: string
    title: string
    purchase_end_date: string
  } | null
  slug: string | null
}

interface LegacySearchResponse {
  data: LegacySearchProduct[] | null
  error: string | null
  debug?: any
}

/**
 * Debug configuration
 */
interface DebugData {
  timing: {
    start: number
    end: number
    duration: number
  }
  params: Record<string, any>
  error?: any
  environment: Record<string, any>
  request: Record<string, any>
}

/**
 * Helper function to check if debug is enabled
 */
function isDebugEnabled(): boolean {
  return process.env.NODE_ENV === 'development' || process.env.DEBUG === 'true'
}

/**
 * GET /api/search
 * 
 * Search products with filtering and sorting
 * 
 * Query Parameters:
 * - q: Search query string
 * - category: Category ID filter
 * - brand: Brand name filter
 * - sort: Sort order (relevance, price_asc, price_desc, newest, featured)
 * - page: Page number (default: 1)
 * - limit: Items per page (default: 20, max: 50)
 */
export async function GET(request: NextRequest): Promise<NextResponse<LegacySearchResponse>> {
  // Apply rate limiting
  const rateLimitResponse = applyRateLimit(request, rateLimits.search)
  if (rateLimitResponse) {
    return rateLimitResponse as NextResponse<LegacySearchResponse>
  }

  const debugData: DebugData = {
    timing: {
      start: Date.now(),
      end: Date.now(),
      duration: 0,
    },
    params: {},
    environment: {
      nodeEnv: process.env.NODE_ENV || 'development',
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    },
    request: {
      url: '/api/search',
      method: 'GET',
      headers: {
        'content-type': 'application/json',
      },
    },
  }

  try {
    // Parse and validate query parameters
    const { searchParams } = new URL(request.url)

    // Validate pagination parameters
    const { page, limit } = validatePaginationParams(
      searchParams.get('page'),
      searchParams.get('limit')
    )

    // Validate and sanitize search parameters
    const rawQuery = searchParams.get('q')
    const rawCategory = searchParams.get('category')
    const rawBrand = searchParams.get('brand')
    const sort = searchParams.get('sort') || 'relevance'

    // Validate search query if provided
    let sanitizedQuery: string | undefined
    if (rawQuery) {
      const queryValidation = validateSearchQuery(rawQuery)
      if (!queryValidation.isValid) {
        return NextResponse.json({
          data: null,
          error: 'Invalid search query. Please remove any suspicious characters.',
        }, { status: 400 })
      }
      sanitizedQuery = queryValidation.sanitized
    }

    // Validate and sanitize filter parameters
    const rawFilters = {
      category: rawCategory,
      brand: rawBrand
    }
    const sanitizedFilters = validateFilterParams(rawFilters)

    // Validate sort parameter
    const validSortOptions = ['relevance', 'price_asc', 'price_desc', 'newest', 'featured']
    if (!validSortOptions.includes(sort)) {
      return NextResponse.json({
        data: null,
        error: `Invalid sort parameter. Must be one of: ${validSortOptions.join(', ')}.`,
      }, { status: 400 })
    }

    debugData.params = {
      query: sanitizedQuery,
      category: sanitizedFilters.category,
      brand: sanitizedFilters.brand,
      sort,
      page,
      limit
    }

    // Return empty results if no search criteria provided
    if (!sanitizedQuery && !sanitizedFilters.category && !sanitizedFilters.brand) {
      return NextResponse.json({ data: [], error: null })
    }

    // Build search filters with validated data
    const filters: SearchFilters = {
      query: sanitizedQuery,
      category: sanitizedFilters.category,
      brand: sanitizedFilters.brand,
      sortBy: sort as any,
    }

    // Search products using shared data layer
    const supabase = createServerSupabaseReadOnlyClient();
    const searchResult = await searchProducts(supabase, filters, page, limit)

    // Transform to legacy format for backward compatibility
    const legacyProducts: LegacySearchProduct[] = searchResult.products.map(product => ({
      id: product.id,
      name: product.name,
      description: product.description,
      images: product.images,
      status: product.status,
      brand: product.brand ? {
        id: product.brand.id,
        name: product.brand.name,
        logo_url: product.brand.logoUrl || '',
      } : null,
      category: product.category ? {
        id: product.category.id,
        name: product.category.name,
      } : null,
      minPrice: product.retailerOffers?.length > 0
        ? Math.min(...product.retailerOffers.map(offer => offer.price))
        : null,
      cashbackAmount: product.cashbackAmount || 0,
      retailerOffers: (product.retailerOffers || []).map(offer => ({
        retailer: {
          name: offer.retailer.name,
          logo_url: offer.retailer.logoUrl || '',
        },
        price: offer.price,
        cashback: product.cashbackAmount || 0,
        url: offer.url || '',
        stock_status: offer.stockStatus || 'unknown',
        created_at: offer.createdAt,
      })),
      promotion: product.promotion ? {
        id: product.promotion.id,
        title: product.promotion.title,
        purchase_end_date: product.promotion.purchaseEndDate,
      } : null,
      slug: product.slug,
    }))

    debugData.timing.end = Date.now()
    debugData.timing.duration = debugData.timing.end - debugData.timing.start

    // Create response with proper caching headers
    const response: LegacySearchResponse = {
      data: legacyProducts,
      error: null,
      ...(isDebugEnabled() && { debug: debugData }),
    }

    const nextResponse = NextResponse.json(response)

    // Set cache headers for search results
    nextResponse.headers.set(
      'Cache-Control',
      'public, s-maxage=300, stale-while-revalidate=60'
    )

    // Add CORS headers for API access
    nextResponse.headers.set('Access-Control-Allow-Origin', '*')
    nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS')
    nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type')

    // Add performance timing header for monitoring
    nextResponse.headers.set('X-Response-Time', `${debugData.timing.duration}ms`)

    return nextResponse

  } catch (error) {
    console.error('Error in search API route:', error)

    debugData.error = error instanceof Error ? error.message : 'An unexpected error occurred'
    debugData.timing.end = Date.now()
    debugData.timing.duration = debugData.timing.end - debugData.timing.start

    // Return standardized error response in legacy format
    const errorResponse: LegacySearchResponse = {
      data: null,
      error: 'Search failed',
      ...(isDebugEnabled() && { debug: debugData }),
    }

    return NextResponse.json(errorResponse, { status: 500 })
  }
}

/**
 * OPTIONS handler for CORS preflight requests
 */
export async function OPTIONS(): Promise<NextResponse> {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}

/**
 * Runtime configuration
 * Using Node.js runtime for server-side data layer compatibility
 */
export const runtime = 'nodejs'

/**
 * Route segment config for caching
 */
export const revalidate = 300 // Revalidate every 5 minutes
