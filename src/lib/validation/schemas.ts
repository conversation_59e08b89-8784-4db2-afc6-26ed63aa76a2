/**
 * Zod Validation Schemas for API Security
 * 
 * This file contains comprehensive validation schemas for all API endpoints
 * to prevent injection attacks and ensure data integrity.
 * 
 * Security Features:
 * - Input length limits to prevent DoS attacks
 * - Type validation to prevent type confusion
 * - Format validation for IDs, emails, etc.
 * - Enum validation for controlled values
 * - Sanitization-friendly string validation
 */

import { z } from 'zod';

// Common validation patterns
const UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
const SLUG_REGEX = /^[a-zA-Z0-9_-]+$/;
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Base schemas for reusable validation
export const baseSchemas = {
  // UUID validation
  uuid: z.string().regex(UUID_REGEX, 'Invalid UUID format'),
  
  // Slug validation (alphanumeric, hyphens, underscores)
  slug: z.string().min(2).max(150).regex(SLUG_REGEX, 'Invalid slug format'),
  
  // ID that can be either UUID or slug
  id: z.string().min(1).max(150).refine((val) => {
    return UUID_REGEX.test(val) || SLUG_REGEX.test(val);
  }, 'ID must be a valid UUID or slug'),
  
  // Email validation
  email: z.string().max(255).regex(EMAIL_REGEX, 'Invalid email format'),
  
  // Safe string (no HTML/script content)
  safeString: z.string().max(255).refine((val) => {
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /on\w+=/i,
      /<iframe/i,
      /<object/i,
      /<embed/i
    ];
    return !suspiciousPatterns.some(pattern => pattern.test(val));
  }, 'String contains potentially dangerous content'),
  
  // Search query validation
  searchQuery: z.string().min(1).max(200).refine((val) => {
    const suspiciousPatterns = [
      /script/i,
      /javascript/i,
      /vbscript/i,
      /onload/i,
      /onerror/i,
      /eval\(/i,
      /expression\(/i
    ];
    return !suspiciousPatterns.some(pattern => pattern.test(val));
  }, 'Search query contains suspicious content'),
  
  // Pagination parameters
  page: z.coerce.number().int().min(1).max(1000).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  
  // Sort options
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
};

// Search API validation schema
export const searchApiSchema = z.object({
  q: baseSchemas.searchQuery.optional(),
  category: baseSchemas.safeString.max(100).optional(),
  brand: baseSchemas.safeString.max(100).optional(),
  sort: z.enum(['relevance', 'price_asc', 'price_desc', 'newest', 'featured']).default('relevance'),
  page: baseSchemas.page,
  limit: baseSchemas.limit.max(50), // Lower limit for search
});

// Search suggestions API schema
export const searchSuggestionsSchema = z.object({
  q: baseSchemas.searchQuery.min(2, 'Query must be at least 2 characters'),
  limit: z.coerce.number().int().min(1).max(10).default(3),
});

// Contact form validation schema
export const contactFormSchema = z.object({
  name: baseSchemas.safeString.min(2, 'Name must be at least 2 characters').max(100),
  email: baseSchemas.email,
  phone: z.string().max(20).regex(/^[\d\s\-\+\(\)]+$/, 'Invalid phone format').optional(),
  enquiryType: z.enum(['general', 'support', 'partnership', 'feedback', 'other']),
  message: baseSchemas.safeString.min(10, 'Message must be at least 10 characters').max(5000),
});

// Products API validation schema
export const productsApiSchema = z.object({
  category_id: baseSchemas.id.optional(),
  brand_id: baseSchemas.id.optional(),
  retailer_id: baseSchemas.id.optional(),
  featured: z.enum(['true', 'false']).optional(),
  status: z.enum(['active', 'inactive']).optional(),
  sort: z.enum(['name', 'price', 'cashback', 'created_at']).default('name'),
  order: baseSchemas.sortOrder,
  page: baseSchemas.page,
  limit: baseSchemas.limit,
});

// Product detail API schema
export const productDetailSchema = z.object({
  id: baseSchemas.id,
});

// Brands API validation schema
export const brandsApiSchema = z.object({
  featured: z.enum(['true', 'false']).optional(),
  status: z.enum(['active', 'inactive']).optional(),
  sort: z.enum(['name', 'created_at']).default('name'),
  order: baseSchemas.sortOrder,
  page: baseSchemas.page,
  limit: baseSchemas.limit,
});

// Brand detail API schema
export const brandDetailSchema = z.object({
  id: baseSchemas.id,
});

// Retailers API validation schema
export const retailersApiSchema = z.object({
  featured: z.enum(['true', 'false']).optional(),
  status: z.enum(['active', 'inactive']).optional(),
  sort: z.enum(['name', 'created_at']).default('name'),
  order: baseSchemas.sortOrder,
  page: baseSchemas.page,
  limit: baseSchemas.limit,
});

// Retailer detail API schema
export const retailerDetailSchema = z.object({
  id: baseSchemas.id,
});

// Categories API validation schema
export const categoriesApiSchema = z.object({
  parent_id: baseSchemas.id.optional(),
  level: z.coerce.number().int().min(1).max(3).optional(),
  sort: z.enum(['name', 'created_at']).default('name'),
  order: baseSchemas.sortOrder,
  page: baseSchemas.page,
  limit: baseSchemas.limit,
});

// Type exports for TypeScript integration
export type SearchApiInput = z.infer<typeof searchApiSchema>;
export type SearchSuggestionsInput = z.infer<typeof searchSuggestionsSchema>;
export type ContactFormInput = z.infer<typeof contactFormSchema>;
export type ProductsApiInput = z.infer<typeof productsApiSchema>;
export type ProductDetailInput = z.infer<typeof productDetailSchema>;
export type BrandsApiInput = z.infer<typeof brandsApiSchema>;
export type BrandDetailInput = z.infer<typeof brandDetailSchema>;
export type RetailersApiInput = z.infer<typeof retailersApiSchema>;
export type RetailerDetailInput = z.infer<typeof retailerDetailSchema>;
export type CategoriesApiInput = z.infer<typeof categoriesApiSchema>;

// Validation helper functions
export const validateInput = <T>(schema: z.ZodSchema<T>, data: unknown) => {
  const result = schema.safeParse(data);
  
  if (!result.success) {
    return {
      success: false,
      error: 'Validation failed',
      details: result.error.flatten(),
    };
  }
  
  return {
    success: true,
    data: result.data,
  };
};

// Error response helper
export const createValidationErrorResponse = (error: string, details?: any) => {
  return {
    data: null,
    error,
    details,
  };
};
