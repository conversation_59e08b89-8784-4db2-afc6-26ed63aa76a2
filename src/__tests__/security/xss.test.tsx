/**
 * XSS Prevention Tests
 * 
 * Tests for Cross-Site Scripting (XSS) prevention in React components
 * and structured data rendering.
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { renderSecureJsonLd } from '@/lib/security/utils';

// Mock components for testing
const MockStructuredDataComponent = ({ data }: { data: any }) => {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: renderSecureJsonLd(data) }}
    />
  );
};

const MockSafeTextComponent = ({ text }: { text: string }) => {
  // This represents the safe way to render user content
  return <div>{text}</div>;
};

describe('XSS Prevention Tests', () => {
  
  describe('JSON-LD Structured Data Security', () => {
    
    it('should escape dangerous characters in JSON-LD', () => {
      const maliciousData = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: 'Test Product',
        description: 'A product with <script>alert("xss")</script> in description'
      };
      
      const { container } = render(<MockStructuredDataComponent data={maliciousData} />);
      const scriptElement = container.querySelector('script[type="application/ld+json"]');
      
      expect(scriptElement).toBeTruthy();
      expect(scriptElement?.innerHTML).not.toContain('<script>');
      expect(scriptElement?.innerHTML).toContain('\\u003c'); // Escaped <
      expect(scriptElement?.innerHTML).toContain('\\u003e'); // Escaped >
    });
    
    it('should handle complex nested objects with XSS attempts', () => {
      const complexMaliciousData = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        offers: [
          {
            '@type': 'Offer',
            seller: {
              name: 'Evil Corp</script><script>alert("nested xss")</script>',
              url: 'javascript:alert("url xss")'
            },
            price: '99.99',
            priceCurrency: 'USD'
          }
        ],
        aggregateRating: {
          '@type': 'AggregateRating',
          ratingValue: '4.5',
          reviewCount: '<img src=x onerror=alert("img xss")>'
        }
      };
      
      const { container } = render(<MockStructuredDataComponent data={complexMaliciousData} />);
      const scriptElement = container.querySelector('script[type="application/ld+json"]');
      
      expect(scriptElement?.innerHTML).not.toContain('</script><script>');
      expect(scriptElement?.innerHTML).not.toContain('javascript:');
      expect(scriptElement?.innerHTML).not.toContain('<img');
      expect(scriptElement?.innerHTML).not.toContain('onerror');
      
      // Should contain escaped versions
      expect(scriptElement?.innerHTML).toContain('\\u003c');
      expect(scriptElement?.innerHTML).toContain('\\u003e');
    });
    
    it('should preserve valid JSON structure while escaping', () => {
      const validData = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: 'Safe Product Name',
        price: 99.99,
        availability: 'InStock'
      };
      
      const { container } = render(<MockStructuredDataComponent data={validData} />);
      const scriptElement = container.querySelector('script[type="application/ld+json"]');
      
      // Should be valid JSON when parsed
      expect(() => {
        JSON.parse(scriptElement?.innerHTML || '');
      }).not.toThrow();
      
      const parsed = JSON.parse(scriptElement?.innerHTML || '');
      expect(parsed['@type']).toBe('Product');
      expect(parsed.name).toBe('Safe Product Name');
    });
  });
  
  describe('React Component XSS Prevention', () => {
    
    it('should safely render user text content', () => {
      const maliciousText = '<script>alert("xss")</script><img src=x onerror=alert(1)>';
      
      render(<MockSafeTextComponent text={maliciousText} />);
      
      // React should automatically escape the content
      expect(screen.getByText(maliciousText)).toBeInTheDocument();
      
      // The actual DOM should not contain executable script tags
      const container = document.body;
      const scriptTags = container.querySelectorAll('script');
      
      // Filter out our test scripts and only check for malicious ones
      const maliciousScripts = Array.from(scriptTags).filter(script => 
        script.innerHTML.includes('alert("xss")')
      );
      
      expect(maliciousScripts).toHaveLength(0);
    });
    
    it('should handle various XSS attack vectors', () => {
      const xssVectors = [
        '<script>alert("basic xss")</script>',
        '<img src=x onerror=alert("img xss")>',
        '<svg onload=alert("svg xss")>',
        '<iframe src="javascript:alert(\'iframe xss\')"></iframe>',
        '<object data="javascript:alert(\'object xss\')"></object>',
        '<embed src="javascript:alert(\'embed xss\')">',
        '<link rel="stylesheet" href="javascript:alert(\'link xss\')">',
        '<style>@import "javascript:alert(\'style xss\')"</style>',
        'javascript:alert("javascript protocol")',
        'vbscript:alert("vbscript protocol")',
        'data:text/html,<script>alert("data uri")</script>',
        '<div onmouseover="alert(\'event handler\')">Hover me</div>'
      ];
      
      xssVectors.forEach((vector, index) => {
        const { unmount } = render(<MockSafeTextComponent text={vector} />);
        
        // Text should be displayed but not executed
        expect(screen.getByText(vector)).toBeInTheDocument();
        
        // Clean up for next test
        unmount();
      });
    });
  });
  
  describe('URL and Protocol Security', () => {
    
    it('should handle dangerous URLs safely', () => {
      const dangerousUrls = [
        'javascript:alert("js url")',
        'vbscript:alert("vb url")',
        'data:text/html,<script>alert("data url")</script>',
        'file:///etc/passwd',
        'ftp://malicious.com/payload.exe'
      ];
      
      dangerousUrls.forEach(url => {
        const data = {
          '@context': 'https://schema.org',
          '@type': 'Product',
          url: url
        };
        
        const { container } = render(<MockStructuredDataComponent data={data} />);
        const scriptElement = container.querySelector('script[type="application/ld+json"]');
        
        // URL should be escaped and not executable
        expect(scriptElement?.innerHTML).not.toContain('javascript:');
        expect(scriptElement?.innerHTML).not.toContain('vbscript:');
        expect(scriptElement?.innerHTML).not.toContain('data:text/html');
      });
    });
  });
  
  describe('Content Security Policy Compliance', () => {
    
    it('should not create inline event handlers', () => {
      const maliciousData = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: 'Product" onload="alert(\'csp bypass\')" data-evil="true'
      };
      
      const { container } = render(<MockStructuredDataComponent data={maliciousData} />);
      const scriptElement = container.querySelector('script[type="application/ld+json"]');
      
      expect(scriptElement?.innerHTML).not.toContain('onload=');
      expect(scriptElement?.innerHTML).not.toContain('data-evil=');
    });
  });
  
  describe('Edge Cases and Error Handling', () => {
    
    it('should handle null and undefined values safely', () => {
      const dataWithNulls = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: null,
        description: undefined,
        price: 0
      };
      
      expect(() => {
        render(<MockStructuredDataComponent data={dataWithNulls} />);
      }).not.toThrow();
    });
    
    it('should handle circular references gracefully', () => {
      const circularData: any = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: 'Circular Product'
      };
      circularData.self = circularData; // Create circular reference
      
      expect(() => {
        renderSecureJsonLd(circularData);
      }).toThrow(); // Should throw due to circular reference
    });
    
    it('should handle very large objects', () => {
      const largeData = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: 'Large Product',
        description: 'x'.repeat(10000), // Very long description
        offers: Array.from({ length: 1000 }, (_, i) => ({
          '@type': 'Offer',
          price: i,
          seller: `Seller ${i}`
        }))
      };
      
      expect(() => {
        render(<MockStructuredDataComponent data={largeData} />);
      }).not.toThrow();
    });
  });
  
  describe('Real-world Attack Scenarios', () => {
    
    it('should prevent stored XSS through product descriptions', () => {
      const storedXssPayload = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: 'Innocent Product',
        description: 'Great product! <script>fetch("/api/steal-data", {method: "POST", body: document.cookie})</script>'
      };
      
      const { container } = render(<MockStructuredDataComponent data={storedXssPayload} />);
      const scriptElement = container.querySelector('script[type="application/ld+json"]');
      
      expect(scriptElement?.innerHTML).not.toContain('<script>');
      expect(scriptElement?.innerHTML).not.toContain('fetch(');
      expect(scriptElement?.innerHTML).not.toContain('document.cookie');
    });
    
    it('should prevent DOM-based XSS through URL parameters', () => {
      // Simulate malicious URL parameter that might be reflected in structured data
      const urlBasedPayload = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: decodeURIComponent('%3Cscript%3Ealert%28%22url%20xss%22%29%3C%2Fscript%3E'),
        url: 'https://example.com/product?name=' + encodeURIComponent('<script>alert("reflected")</script>')
      };
      
      const { container } = render(<MockStructuredDataComponent data={urlBasedPayload} />);
      const scriptElement = container.querySelector('script[type="application/ld+json"]');
      
      expect(scriptElement?.innerHTML).not.toContain('<script>alert');
      expect(scriptElement?.innerHTML).toContain('\\u003c');
    });
  });
});
