/**
 * Security Validation Tests
 * 
 * Comprehensive tests for input validation, XSS prevention, and injection attack protection.
 * These tests ensure our security measures are working correctly.
 */

import { 
  searchApiSchema, 
  contactFormSchema, 
  validateInput,
  createValidationErrorResponse 
} from '@/lib/validation/schemas';
import { 
  sanitizeString, 
  validateSearchQuery, 
  renderSecureJsonLd,
  sanitizeHtml 
} from '@/lib/security/utils';

describe('Security Validation Tests', () => {
  
  describe('Zod Schema Validation', () => {
    
    describe('Search API Schema', () => {
      it('should accept valid search parameters', () => {
        const validInput = {
          q: 'laptop',
          category: 'electronics',
          brand: 'apple',
          sort: 'relevance',
          page: '1',
          limit: '20'
        };
        
        const result = validateInput(searchApiSchema, validInput);
        expect(result.success).toBe(true);
        if (result.success) {
          expect(result.data.q).toBe('laptop');
          expect(result.data.sort).toBe('relevance');
        }
      });
      
      it('should reject malicious search queries', () => {
        const maliciousInputs = [
          { q: '<script>alert("xss")</script>' },
          { q: 'javascript:alert(1)' },
          { q: 'onload=alert(1)' },
          { q: 'eval(document.cookie)' },
          { q: 'SELECT * FROM users' }
        ];
        
        maliciousInputs.forEach(input => {
          const result = validateInput(searchApiSchema, input);
          expect(result.success).toBe(false);
        });
      });
      
      it('should reject oversized inputs', () => {
        const oversizedInput = {
          q: 'a'.repeat(201), // Exceeds 200 char limit
          category: 'b'.repeat(101) // Exceeds 100 char limit
        };
        
        const result = validateInput(searchApiSchema, oversizedInput);
        expect(result.success).toBe(false);
      });
      
      it('should reject invalid sort parameters', () => {
        const invalidSort = {
          q: 'laptop',
          sort: 'invalid_sort'
        };
        
        const result = validateInput(searchApiSchema, invalidSort);
        expect(result.success).toBe(false);
      });
    });
    
    describe('Contact Form Schema', () => {
      it('should accept valid contact form data', () => {
        const validInput = {
          name: 'John Doe',
          email: '<EMAIL>',
          phone: '+44 ************',
          enquiryType: 'general',
          message: 'This is a valid message with sufficient length.'
        };
        
        const result = validateInput(contactFormSchema, validInput);
        expect(result.success).toBe(true);
      });
      
      it('should reject malicious contact form data', () => {
        const maliciousInputs = [
          {
            name: '<script>alert("xss")</script>',
            email: '<EMAIL>',
            enquiryType: 'general',
            message: 'Valid message'
          },
          {
            name: 'John',
            email: 'javascript:alert(1)@example.com',
            enquiryType: 'general',
            message: 'Valid message'
          },
          {
            name: 'John',
            email: '<EMAIL>',
            enquiryType: 'general',
            message: '<iframe src="javascript:alert(1)"></iframe>'
          }
        ];
        
        maliciousInputs.forEach(input => {
          const result = validateInput(contactFormSchema, input);
          expect(result.success).toBe(false);
        });
      });
      
      it('should reject invalid email formats', () => {
        const invalidEmails = [
          'not-an-email',
          '@example.com',
          'test@',
          '<EMAIL>'
        ];
        
        invalidEmails.forEach(email => {
          const input = {
            name: 'John',
            email,
            enquiryType: 'general',
            message: 'Valid message with sufficient length.'
          };
          
          const result = validateInput(contactFormSchema, input);
          expect(result.success).toBe(false);
        });
      });
    });
  });
  
  describe('String Sanitization', () => {
    
    it('should remove dangerous HTML characters', () => {
      const maliciousInputs = [
        '<script>alert("xss")</script>',
        '<img src=x onerror=alert(1)>',
        'javascript:alert(1)',
        'vbscript:alert(1)',
        'data:text/html,<script>alert(1)</script>'
      ];
      
      maliciousInputs.forEach(input => {
        const sanitized = sanitizeString(input);
        expect(sanitized).not.toContain('<');
        expect(sanitized).not.toContain('>');
        expect(sanitized).not.toContain('javascript:');
        expect(sanitized).not.toContain('vbscript:');
        expect(sanitized).not.toContain('data:');
      });
    });
    
    it('should respect length limits', () => {
      const longString = 'a'.repeat(1000);
      const sanitized = sanitizeString(longString, 100);
      expect(sanitized.length).toBeLessThanOrEqual(100);
    });
    
    it('should handle null and undefined inputs', () => {
      expect(sanitizeString(null)).toBe('');
      expect(sanitizeString(undefined)).toBe('');
      expect(sanitizeString('')).toBe('');
    });
  });
  
  describe('Search Query Validation', () => {
    
    it('should accept safe search queries', () => {
      const safeQueries = [
        'laptop',
        'apple macbook',
        'gaming mouse 2024',
        'wireless headphones'
      ];
      
      safeQueries.forEach(query => {
        const result = validateSearchQuery(query);
        expect(result.isValid).toBe(true);
        expect(result.sanitized).toBe(query);
      });
    });
    
    it('should reject dangerous search queries', () => {
      const dangerousQueries = [
        '<script>alert(1)</script>',
        'javascript:alert(1)',
        'eval(document.cookie)',
        'onload=alert(1)',
        'SELECT * FROM users WHERE id=1'
      ];
      
      dangerousQueries.forEach(query => {
        const result = validateSearchQuery(query);
        expect(result.isValid).toBe(false);
      });
    });
  });
  
  describe('JSON-LD Security', () => {
    
    it('should safely render JSON-LD data', () => {
      const testData = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        name: 'Test Product',
        description: 'A <script>alert("xss")</script> product'
      };
      
      const rendered = renderSecureJsonLd(testData);
      
      // Should escape dangerous characters
      expect(rendered).toContain('\\u003c'); // Escaped <
      expect(rendered).toContain('\\u003e'); // Escaped >
      expect(rendered).not.toContain('<script>');
    });
    
    it('should handle complex nested objects', () => {
      const complexData = {
        '@context': 'https://schema.org',
        '@type': 'Product',
        offers: [
          {
            '@type': 'Offer',
            seller: {
              name: 'Evil Corp <script>alert(1)</script>'
            }
          }
        ]
      };
      
      const rendered = renderSecureJsonLd(complexData);
      expect(rendered).not.toContain('<script>');
      expect(rendered).toContain('\\u003c');
    });
  });
  
  describe('HTML Sanitization', () => {
    
    it('should allow safe HTML tags', () => {
      const input = '<p>This is <b>bold</b> and <i>italic</i> text.</p>';
      const sanitized = sanitizeHtml(input, {
        allowedTags: ['p', 'b', 'i'],
        allowedAttributes: []
      });
      
      expect(sanitized).toContain('<p>');
      expect(sanitized).toContain('<b>');
      expect(sanitized).toContain('<i>');
    });
    
    it('should remove dangerous HTML', () => {
      const input = '<p>Safe text</p><script>alert("xss")</script><img src=x onerror=alert(1)>';
      const sanitized = sanitizeHtml(input, {
        allowedTags: ['p'],
        allowedAttributes: []
      });
      
      expect(sanitized).toContain('<p>');
      expect(sanitized).not.toContain('<script>');
      expect(sanitized).not.toContain('<img');
      expect(sanitized).not.toContain('onerror');
    });
  });
  
  describe('Error Response Creation', () => {
    
    it('should create proper error responses', () => {
      const error = createValidationErrorResponse('Test error', { field: 'invalid' });
      
      expect(error.data).toBeNull();
      expect(error.error).toBe('Test error');
      expect(error.details).toEqual({ field: 'invalid' });
    });
  });
});
